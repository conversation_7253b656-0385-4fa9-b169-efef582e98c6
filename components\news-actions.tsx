"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Trash2,
  Send,
  MoreVertical,
  ExternalLink,
  Copy,
  CheckCircle,
  UserX,
  BookOpen,
  BookOpenCheck,
} from "lucide-react";
import { toast } from "sonner";
import { useExcludedSources } from "@/hooks/useExcludedSources";
import { useMarkAsRead } from "@/hooks/useMarkAsRead";

interface NewsActionsProps {
  newsKey: string;
  newsUrl: string;
  newsAuthor: string;
  selected: boolean;
  onDelete: (key: string) => void;
  onSend: () => void;
  onMarkAsReadToggle?: (key: string, currentSelected: boolean) => void;
  onRefresh?: () => void;
  isDeleting: boolean;
  isSending: boolean;
}

export function NewsActions({
  newsKey,
  newsUrl,
  newsAuthor,
  selected,
  onDelete,
  onSend,
  onMarkAsReadToggle,
  onRefresh,
  isDeleting,
  isSending,
}: NewsActionsProps) {
  const [copied, setCopied] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Hook para detectar tamanho da tela
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768); // sm breakpoint
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Excluded sources hook
  const { isProcessing: isExcludingSource, addExcludedSources } =
    useExcludedSources({
      onSuccess: (response, action, sources) => {
        console.log(`Successfully ${action}ed sources:`, sources);
        // Excluir a notícia após excluir a fonte
        onDelete(newsKey);
      },
      onError: (error) => {
        console.error("Excluded sources operation failed:", error);
      },
    });

  // Mark as read hook
  const { isUpdating: isMarkingAsRead, toggleMarkAsRead } = useMarkAsRead({
    onSuccess: (key, isRead) => {
      onMarkAsReadToggle?.(key, !isRead); // Call parent callback if provided
      // Forçar refresh da lista após marcar como lido
      onRefresh?.();
    },
    onError: (error) => {
      console.error("Mark as read operation failed:", error);
    },
  });

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(newsUrl);
      setCopied(true);
      toast.success("URL copiada para a área de transferência");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Erro ao copiar URL");
    }
  };

  const handleExcludeSource = async () => {
    if (!newsAuthor) {
      toast.error("Autor da notícia não encontrado");
      return;
    }

    try {
      await addExcludedSources([newsAuthor]);
    } catch (error) {
      console.error("Error excluding source:", error);
    }
  };

  const handleMarkAsRead = async () => {
    try {
      await toggleMarkAsRead(newsKey, selected);
    } catch (error) {
      console.error("Error toggling mark as read:", error);
    }
  };

  const handleSend = async () => {
    try {
      // Primeiro executa o envio original
      onSend();

      // Depois marca como lido se ainda não estiver marcado
      if (!selected) {
        await toggleMarkAsRead(newsKey, false); // false porque queremos marcar como lido (true)
      }
    } catch (error) {
      console.error("Error sending news:", error);
    }
  };

  // Componente responsivo para confirmação de exclusão
  const DeleteConfirmationDialog = () => {
    if (isMobile) {
      return (
        <Drawer>
          <DrawerTrigger asChild>
            <Button
              variant="outline"
              disabled={isDeleting}
              className="flex-1 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 hover:text-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 h-10 sm:h-11 text-sm font-medium"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2 flex-shrink-0" />
                  <span className="hidden sm:inline">Excluindo...</span>
                  <span className="sm:hidden">...</span>
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>Excluir</span>
                </>
              )}
            </Button>
          </DrawerTrigger>
          <DrawerContent className="mx-4">
            <DrawerHeader className="text-left">
              <DrawerTitle className="text-base font-semibold text-gray-900">
                Confirmar exclusão
              </DrawerTitle>
              <DrawerDescription className="text-sm text-gray-600">
                Esta ação não pode ser desfeita. A notícia será permanentemente
                removida do sistema.
              </DrawerDescription>
            </DrawerHeader>
            <DrawerFooter className="pt-2">
              <div className="flex flex-col-reverse gap-2 w-full">
                <DrawerClose asChild>
                  <Button variant="outline" className="w-full text-sm">
                    Cancelar
                  </Button>
                </DrawerClose>
                <Button
                  onClick={() => onDelete(newsKey)}
                  disabled={isDeleting}
                  className="w-full bg-red-600 hover:bg-red-700 focus:ring-2 focus:ring-red-500 text-sm"
                >
                  {isDeleting ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2 flex-shrink-0" />
                      Excluindo...
                    </>
                  ) : (
                    "Confirmar exclusão"
                  )}
                </Button>
              </div>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      );
    }

    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outline"
            disabled={isDeleting}
            className="flex-1 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 hover:text-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 h-10 sm:h-11 text-sm font-medium"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2 flex-shrink-0" />
                <span className="hidden sm:inline">Excluindo...</span>
                <span className="sm:hidden">...</span>
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>Excluir</span>
              </>
            )}
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent className="mx-4 max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-base sm:text-lg font-semibold text-gray-900">
              Confirmar exclusão
            </AlertDialogTitle>
            <AlertDialogDescription className="text-sm text-gray-600 mt-2">
              Esta ação não pode ser desfeita. A notícia será permanentemente
              removida do sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col-reverse sm:flex-row gap-2 sm:gap-3">
            <AlertDialogCancel className="w-full sm:w-auto text-sm">
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => onDelete(newsKey)}
              disabled={isDeleting}
              className="w-full sm:w-auto bg-red-600 hover:bg-red-700 focus:ring-2 focus:ring-red-500 text-sm"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2 flex-shrink-0" />
                  Excluindo...
                </>
              ) : (
                "Confirmar exclusão"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <div className="flex flex-col gap-2 w-full">
      {/* Primeira linha: Botões Enviar e Excluir */}
      <div className="flex items-center gap-2">
        {/* Botão de Enviar */}
        <Button
          onClick={handleSend}
          disabled={isSending}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white shadow-sm transition-all duration-200 hover:shadow-md focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 h-10 sm:h-11 text-sm font-medium"
        >
          {isSending ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 flex-shrink-0" />
              <span className="hidden sm:inline">Enviando...</span>
              <span className="sm:hidden">...</span>
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2 flex-shrink-0" />
              <span>Enviar</span>
            </>
          )}
        </Button>

        {/* Delete Confirmation Dialog - Responsivo */}
        <DeleteConfirmationDialog />
      </div>

      {/* Segunda linha: Botão Marcar (maior) + Dropdown */}
      <div className="flex items-center gap-2">
        {/* Botão de Marcar como Lido/Não Lido - Tamanho maior */}
        <Button
          variant="outline"
          onClick={handleMarkAsRead}
          disabled={isMarkingAsRead}
          className={`flex-1 transition-all duration-200 focus:ring-2 focus:ring-offset-2 h-10 sm:h-11 text-sm font-medium ${
            selected
              ? "border-green-200 bg-green-50 text-green-700 hover:bg-green-100 hover:border-green-300 hover:text-green-800 focus:ring-green-500"
              : "border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 focus:ring-gray-500"
          }`}
        >
          {isMarkingAsRead ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2 flex-shrink-0" />
              <span className="hidden sm:inline">Atualizando...</span>
              <span className="sm:hidden">...</span>
            </>
          ) : (
            <>
              {selected ? (
                <BookOpenCheck className="h-4 w-4 mr-2 flex-shrink-0" />
              ) : (
                <BookOpen className="h-4 w-4 mr-2 flex-shrink-0" />
              )}
              <span className="hidden sm:inline">
                {selected ? "Marcado como lido" : "Marcar como lido"}
              </span>
              <span className="sm:hidden">
                {selected ? "✓ Lido" : "Marcar"}
              </span>
            </>
          )}
        </Button>

        {/* Dropdown Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="h-10 w-10 sm:h-11 sm:w-11 p-0 hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex-shrink-0"
              aria-label="Mais opções"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-44 sm:w-48 shadow-lg border border-gray-200"
            sideOffset={4}
          >
            <DropdownMenuItem
              onClick={() => window.open(newsUrl, "_blank")}
              className="cursor-pointer hover:bg-gray-50 focus:bg-gray-50 text-sm"
            >
              <ExternalLink className="h-4 w-4 mr-2 text-gray-500 flex-shrink-0" />
              <span className="text-gray-700 truncate">Abrir notícia</span>
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={handleCopyUrl}
              className="cursor-pointer hover:bg-gray-50 focus:bg-gray-50 text-sm"
            >
              {copied ? (
                <CheckCircle className="h-4 w-4 mr-2 text-green-600 flex-shrink-0" />
              ) : (
                <Copy className="h-4 w-4 mr-2 text-gray-500 flex-shrink-0" />
              )}
              <span
                className={`truncate ${
                  copied ? "text-green-700" : "text-gray-700"
                }`}
              >
                {copied ? "Copiado!" : "Copiar URL"}
              </span>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={handleExcludeSource}
              disabled={isExcludingSource}
              className="cursor-pointer text-orange-600 hover:bg-orange-50 focus:bg-orange-50 focus:text-orange-700 text-sm"
            >
              <UserX className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">
                {isExcludingSource ? "Excluindo fonte..." : "Excluir fonte"}
              </span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
