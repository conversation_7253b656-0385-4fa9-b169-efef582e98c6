"use client";

import Image from "next/image";
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { NewsActions } from "@/components/news-actions";
import { User, ExternalLink, Eye, Calendar, CheckCircle2 } from "lucide-react";
import { NewsItem } from "@/types/news";
import { useNewsActions } from "@/hooks/useNewsActions";
import { parseBrazilianDate, formatBrazilianDate } from "@/lib/utils";

interface NewsCardProps {
  newsItem: NewsItem;
  newsKey: string;
  onDelete: (key: string) => void;
  onRefresh?: () => void;
}

export function NewsCard({
  newsItem,
  newsKey,
  onDelete,
  onRefresh,
}: NewsCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Use the new actions hook
  const { isDeleting, isSending, deleteNews, sendNews } = useNewsActions({
    onDeleteSuccess: (key) => onDelete(key),
    onSendSuccess: () => {
      // Success handling is done by the hook
    },
  });

  const handleDelete = async () => {
    await deleteNews(newsKey);
  };

  const handleSend = async () => {
    if (!newsItem.newsUrl) {
      return;
    }
    await sendNews(newsItem.newsUrl, newsKey);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = parseBrazilianDate(dateString);
      if (!date) {
        return dateString;
      }

      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) return "Hoje";
      if (diffDays === 2) return "Ontem";
      if (diffDays <= 7) return `${diffDays - 1} dias atrás`;

      return formatBrazilianDate(date);
    } catch {
      return dateString;
    }
  };

  return (
    <Card className="group relative overflow-hidden transition-all duration-300 hover:shadow-xl sm:hover:shadow-2xl sm:hover:shadow-blue-500/10 hover:-translate-y-1 sm:hover:-translate-y-2 bg-white border border-gray-200/60 h-full flex flex-col backdrop-blur-sm hover:border-blue-300/50">
      {/* Gradient overlay for selection */}
      {newsItem.selected && (
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 pointer-events-none" />
      )}

      {/* Header com Autor */}
      <CardHeader className="pb-2 sm:pb-4 p-3 sm:p-6 flex-shrink-0 relative">
        <div className="flex items-start justify-between gap-2 sm:gap-4">
          <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
            <Avatar className="h-8 w-8 sm:h-12 sm:w-12 ring-1 sm:ring-2 ring-white shadow-md sm:shadow-lg flex-shrink-0 transition-transform duration-300 sm:group-hover:scale-110">
              <AvatarImage
                src={newsItem.newsAuthorImg}
                alt={newsItem.newsAuthor}
                onLoad={() => setImageLoaded(true)}
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-to-br from-blue-600 to-purple-700 text-white font-bold text-xs sm:text-sm">
                {newsItem.newsAuthor
                  .split(" ")
                  .map((n) => n[0])
                  .join("")
                  .slice(0, 2)
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1 space-y-0.5 sm:space-y-1">
              <div className="flex items-center space-x-1.5 sm:space-x-2">
                <User className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-gray-500 flex-shrink-0" />
                <p className="font-semibold text-gray-900 text-xs sm:text-base truncate">
                  {newsItem.newsAuthor}
                </p>
              </div>
              <div className="flex items-center space-x-1.5 sm:space-x-2">
                <Calendar className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-gray-400 flex-shrink-0" />
                <p className="text-xs sm:text-sm text-gray-600 truncate">
                  {formatDate(newsItem.newsDate)}
                </p>
              </div>
            </div>
          </div>

          {newsItem.selected && (
            <CheckCircle2 className="h-8 w-8 flex-shrink-0 text-green-600" />
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-2 sm:space-y-5 flex-1 flex flex-col p-3 sm:p-6 pt-0">
        {/* Título */}
        {newsItem.newsTitle && (
          <h3 className="font-bold text-sm sm:text-xl leading-tight text-gray-900 line-clamp-2 group-hover:text-blue-700 transition-colors duration-300">
            {newsItem.newsTitle}
          </h3>
        )}

        {/* Imagem */}
        {newsItem.newsImg && !imageError && (
          <div className="relative overflow-hidden rounded-lg sm:rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 shadow-inner">
            <div
              className={`aspect-video transition-all duration-300 sm:duration-500 ${
                imageLoaded ? "opacity-100" : "opacity-0"
              }`}
            >
              <Image
                src={newsItem.newsImg}
                alt={newsItem.newsTitle || "Notícia"}
                fill
                className="object-cover transition-all duration-300 sm:duration-700 sm:group-hover:scale-110 sm:group-hover:brightness-105"
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                priority={false}
              />
              {/* Overlay gradiente sutil - apenas desktop */}
              <div className="hidden sm:block absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
            {!imageLoaded && (
              <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse flex items-center justify-center">
                <div className="w-6 h-6 sm:w-10 sm:h-10 border-2 sm:border-3 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
              </div>
            )}
          </div>
        )}

        {/* Conteúdo */}
        <div className="space-y-2 sm:space-y-4 flex-1">
          <p className="text-gray-700 text-xs sm:text-base leading-relaxed line-clamp-2 sm:line-clamp-3">
            {newsItem.newsContent}
          </p>

          {newsItem.newsUrl && (
            <a
              href={newsItem.newsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-1.5 sm:space-x-2 text-xs sm:text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg px-2 py-2 -mx-2 -my-1 hover:bg-blue-50 touch-manipulation"
              aria-label="Ler artigo completo em nova aba"
            >
              <Eye className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="hidden sm:inline">Ler artigo completo</span>
              <span className="sm:hidden">Ler mais</span>
              <ExternalLink className="h-2.5 w-2.5 sm:h-3 sm:w-3 flex-shrink-0 opacity-70" />
            </a>
          )}
        </div>

        {/* Ações - Sempre no final */}
        <div className="flex justify-center pt-3 sm:pt-5 border-t border-gray-200/70 mt-auto bg-gradient-to-r from-gray-50/50 to-gray-100/50 -mx-3 sm:-mx-6 px-3 sm:px-6 pb-0.5 sm:pb-1">
          <div className="w-full max-w-xs sm:max-w-none">
            <NewsActions
              newsKey={newsKey}
              newsUrl={newsItem.newsUrl}
              newsAuthor={newsItem.newsAuthor}
              selected={newsItem.selected}
              onDelete={handleDelete}
              onSend={handleSend}
              onRefresh={onRefresh}
              isDeleting={isDeleting}
              isSending={isSending}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
